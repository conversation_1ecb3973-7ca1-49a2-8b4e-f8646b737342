# Quick MCP + Yolo Setup for Codex CLI

## Create config file:
```bash
mkdir -p ~/.codex && cat > ~/.codex/config.toml << 'EOF'
# Yolo Mode (Full Access, No Prompts)
approval_policy = "never"
sandbox_mode = "danger-full-access"

# MCP Servers
[mcp_servers.playwright-extension]
command = "npx"
args = ["@playwright/mcp@latest", "--extension"]

[mcp_servers.context7]
command = "npx"
args = ["-y", "@upstash/context7-mcp"]

[mcp_servers.sequential-thinking]
command = "npx"
args = ["-y", "@modelcontextprotocol/server-sequential-thinking"]
EOF
```

## Verify setup:
```bash
codex
# Then run: /mcp
```

## Key points:
- Config goes in `~/.codex/config.toml` (not `~/.config/codex/`)
- Use `[mcp_servers.name]` format (not `[[mcp]]`)
- Yolo mode = `approval_policy = "never"` + `sandbox_mode = "danger-full-access"`